"use client"

import { useEffect, useState, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { createSupabaseClient } from "@/lib/supabase/client"

function AuthCallbackContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState('Processing authentication...')

  useEffect(() => {
    const handleAuthCallback = async () => {
      console.log('Auth callback started')
      const supabase = createSupabaseClient()

      try {
        // Handle the OAuth callback
        console.log('Getting session...')
        const { data, error } = await supabase.auth.getSession()

        if (error) {
          console.error('Auth callback error:', error)
          setStatus('error')
          setMessage('Authentication failed. Please try again.')
          setTimeout(() => router.push('/login'), 3000)
          return
        }

        console.log('Session data:', data.session?.user?.id)

        if (data.session?.user) {
          setStatus('success')
          setMessage('Authentication successful! Redirecting...')

          // Check if user exists in our users table, if not create them
          const { data: existingUser, error: userCheckError } = await supabase
            .from('users')
            .select('id, role')
            .eq('id', data.session.user.id)
            .single()

          let userRole = 'user'

          if (userCheckError || !existingUser) {
            console.log('Creating new user profile for Google OAuth user')

            // Create user profile for Google OAuth users
            const newUserData = {
              id: data.session.user.id,
              email: data.session.user.email!,
              name: data.session.user.user_metadata?.full_name || data.session.user.user_metadata?.name || null,
              avatar: data.session.user.user_metadata?.avatar_url || null,
              profile_picture_url: data.session.user.user_metadata?.avatar_url || null,
              role: 'user' as const
            }

            const { error: insertError } = await supabase
              .from('users')
              .insert(newUserData)

            if (insertError) {
              console.error('Error creating user profile:', insertError)
              setStatus('error')
              setMessage('Failed to create user profile. Please try again.')
              setTimeout(() => router.push('/login'), 3000)
              return
            }

            // Send welcome email for new Google users
            try {
              await fetch('/api/send-welcome-email', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  userId: data.session.user.id,
                  userEmail: data.session.user.email,
                  userName: newUserData.name || 'New User',
                  userRole: 'user'
                })
              })
            } catch (emailError) {
              console.log('Welcome email failed (non-critical):', emailError)
            }
          } else {
            userRole = existingUser.role
          }

          // Always redirect to timeline for the best user experience
          let redirectTo = '/timeline'

          // Admins can go to dashboard if specified
          if (userRole === 'admin' && searchParams.get('next') === '/dashboard') {
            redirectTo = '/dashboard'
          }

          // Use next parameter if provided, otherwise use timeline
          const finalRedirect = searchParams.get('next') || redirectTo

          console.log('Redirecting to:', finalRedirect)

          // Small delay to show success message, then redirect
          setTimeout(() => {
            console.log('Executing redirect to:', finalRedirect)
            // Try multiple redirect methods for better mobile compatibility
            try {
              router.replace(finalRedirect)
            } catch (routerError) {
              console.log('Router redirect failed, using window.location:', routerError)
              window.location.href = finalRedirect
            }
          }, 1500)
        } else {
          setStatus('error')
          setMessage('No session found. Please try signing in again.')
          setTimeout(() => router.push('/login'), 3000)
        }
      } catch (error) {
        console.error('Unexpected auth callback error:', error)
        setStatus('error')
        setMessage('An unexpected error occurred. Please try again.')
        setTimeout(() => router.push('/login'), 3000)
      }
    }

    handleAuthCallback()
  }, [router, searchParams])

  return (
    <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center px-4 sm:px-6">
      <div className="max-w-md w-full bg-white rounded-lg p-8 shadow-sm text-center">
        <div className="mb-6">
          {status === 'loading' && (
            <div className="w-12 h-12 border-4 border-gray-300 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          )}
          {status === 'success' && (
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
          )}
          {status === 'error' && (
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
          )}
        </div>

        <h1 className="text-xl font-serif text-gray-800 mb-2">
          {status === 'loading' && 'Authenticating...'}
          {status === 'success' && 'Welcome to OnlyDiary!'}
          {status === 'error' && 'Authentication Failed'}
        </h1>

        <p className="text-gray-600 font-serif">
          {message}
        </p>

        {status === 'error' && (
          <div className="mt-6">
            <button
              onClick={() => router.push('/login')}
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              Return to Sign In
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default function AuthCallback() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center px-4 sm:px-6">
        <div className="max-w-md w-full bg-white rounded-lg p-8 shadow-sm text-center">
          <div className="w-12 h-12 border-4 border-gray-300 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <h1 className="text-xl font-serif text-gray-800 mb-2">Loading...</h1>
          <p className="text-gray-600 font-serif">Processing authentication...</p>
        </div>
      </div>
    }>
      <AuthCallbackContent />
    </Suspense>
  )
}
