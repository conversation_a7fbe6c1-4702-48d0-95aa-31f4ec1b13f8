"use client"

import { useEffect, useState, Suspense } from "react"
import { useRouter, useSearchParams } from "next/navigation"

function AuthCallbackContent() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [message, setMessage] = useState('Processing authentication...')

  useEffect(() => {
    // Check if we have an error from the route handler
    const error = searchParams.get('error')

    if (error) {
      setMessage('Authentication failed. Redirecting to login...')
      setTimeout(() => router.push('/login'), 2000)
      return
    }

    // If no error, show processing message and let route handler handle the redirect
    setMessage('Authentication successful! Redirecting...')

    // Fallback redirect in case route handler doesn't work
    setTimeout(() => {
      const next = searchParams.get('next') || '/timeline'
      router.replace(next)
    }, 3000)
  }, [router, searchParams])

  return (
    <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center px-4 sm:px-6">
      <div className="max-w-md w-full bg-white rounded-lg p-8 shadow-sm text-center">
        <div className="mb-6">
          {status === 'loading' && (
            <div className="w-12 h-12 border-4 border-gray-300 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          )}
          {status === 'success' && (
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
          )}
          {status === 'error' && (
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </div>
          )}
        </div>

        <h1 className="text-xl font-serif text-gray-800 mb-2">
          {status === 'loading' && 'Authenticating...'}
          {status === 'success' && 'Welcome to OnlyDiary!'}
          {status === 'error' && 'Authentication Failed'}
        </h1>

        <p className="text-gray-600 font-serif">
          {message}
        </p>

        {status === 'error' && (
          <div className="mt-6">
            <button
              onClick={() => router.push('/login')}
              className="text-blue-600 hover:text-blue-700 font-medium"
            >
              Return to Sign In
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

export default function AuthCallback() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-[#F7F7F7] flex items-center justify-center px-4 sm:px-6">
        <div className="max-w-md w-full bg-white rounded-lg p-8 shadow-sm text-center">
          <div className="w-12 h-12 border-4 border-gray-300 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
          <h1 className="text-xl font-serif text-gray-800 mb-2">Loading...</h1>
          <p className="text-gray-600 font-serif">Processing authentication...</p>
        </div>
      </div>
    }>
      <AuthCallbackContent />
    </Suspense>
  )
}
